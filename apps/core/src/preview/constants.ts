/**
 * Preview Service Constants
 *
 * This file contains all the magic numbers and configuration constants
 * used throughout the preview service to avoid hardcoded values.
 */

// ==================== TIME CONSTANTS ====================

/** Health check timeout - 30 seconds in milliseconds */
export const THIRTY_SEC_MS = 30000;

/** Health check polling interval - 500 milliseconds */
export const HEALTH_CHECK_INTERVAL_MS = 500;

/** Service ready check interval - 10 seconds in milliseconds */
export const SERVICE_CHECK_INTERVAL_MS = 10000;

/** Auto-destruction timeout - 2 minutes in milliseconds */
export const TWO_MINUTES_MS = 120000;

/** Service ready max wait time - 4 minutes (24 attempts * 10s) */
export const SERVICE_READY_MAX_ATTEMPTS = 24;

// ==================== PORT CONSTANTS ====================

/** Main application port for Next.js and API routes */
export const APP_PORT = 3000;

// ==================== SCALING CONSTANTS ====================

/** Minimum number of instances */
export const MIN_INSTANCES = 1;

/** Maximum number of instances */
export const MAX_INSTANCES = 1;

// ==================== STRING MANIPULATION CONSTANTS ====================

/** Random ID substring start position */
export const RANDOM_ID_START = 2;

/** Random ID substring end position */
export const RANDOM_ID_END = 11;

/** JSON stringify indentation for logging */
export const JSON_INDENT_SPACES = 2;

// ==================== ARRAY INDEX CONSTANTS ====================

/** First domain index in domains array */
export const FIRST_DOMAIN_INDEX = 0;

/** Minimum messages array length to log */
export const MIN_MESSAGES_LENGTH = 0;

// ==================== ENVIRONMENT DEFAULTS ====================

/** Default preview timeout environment variable fallback */
export const DEFAULT_PREVIEW_TIMEOUT = TWO_MINUTES_MS.toString();

// ==================== KOYEB SERVICE CONFIGURATION ====================

/** Koyeb service type for web services */
export const KOYEB_SERVICE_TYPE = 'WEB';

/** Koyeb instance type */
export const KOYEB_INSTANCE_TYPE = 'small';

/** Koyeb region */
export const KOYEB_REGION = 'sin';

/** HTTP protocol string */
export const HTTP_PROTOCOL = 'http';

// ==================== API PATHS ====================

/** Code injection API path */
export const INJECT_CODE_PATH = '/api/inject-code';

/** Health check API path */
export const HEALTH_CHECK_PATH = '/api/health';

/** Root path */
export const ROOT_PATH = '/';

// ==================== HEALTH CHECK CONSTANTS ====================

/** App status indicating healthy state */
export const HEALTHY_STATUS = 'HEALTHY';

/** App status indicating unhealthy state */
export const UNHEALTHY_STATUS = 'UNHEALTHY';

/** App status indicating deleted state */
export const DELETED_STATUS = 'DELETED';

/** App status indicating starting state */
export const STARTING_STATUS = 'STARTING';

/** Deployment status indicating error */
export const ERROR_STATUS = 'ERROR';

/** Deployment status indicating stopped */
export const STOPPED_STATUS = 'STOPPED';

// ==================== TEST CONSTANTS ====================

/** Test timeout - 5 seconds in milliseconds */
export const FIVE_SEC_MS = 5000;

/** Test short interval - 100 milliseconds */
export const TEST_SHORT_INTERVAL_MS = 100;

/** Test timeout - 1 second in milliseconds */
export const ONE_SEC_MS = 1000;

/** Test interval - 200 milliseconds */
export const TEST_INTERVAL_MS = 200;

/** Test timeout - 10 seconds in milliseconds */
export const TEN_SEC_MS = 10000;

/** Test timeout - 35 seconds in milliseconds */
export const THIRTY_FIVE_SEC_MS = 35000;

/** Single file count for tests */
export const SINGLE_FILE_COUNT = 1;

// ==================== FLY.IO SERVICE CONFIGURATION ====================

/** Fly.io machine CPU count */
export const FLY_MACHINE_CPUS = 1;

/** Fly.io machine CPU kind */
export const FLY_MACHINE_CPU_KIND = 'shared';

/** Fly.io machine memory in MB */
export const FLY_MACHINE_MEMORY_MB = 512;

/** Fly.io region */
export const FLY_REGION = 'sin';

/** Fly.io machine restart policy */
export const FLY_RESTART_POLICY = 'no';

/** Fly.io machine auto destroy */
export const FLY_AUTO_DESTROY = true;

// ==================== FLY.IO MACHINE STATES ====================

/** Machine state indicating created */
export const CREATED_STATE = 'created';

/** Machine state indicating starting */
export const STARTING_STATE = 'starting';

/** Machine state indicating started */
export const STARTED_STATE = 'started';

/** Machine state indicating stopped */
export const STOPPED_STATE = 'stopped';

/** Machine state indicating destroyed */
export const DESTROYED_STATE = 'destroyed';

/** Machine state indicating suspended */
export const SUSPENDED_STATE = 'suspended';

// ==================== PROVIDER CONFIGURATION ====================

/** Preview provider types */
export const PREVIEW_PROVIDERS = {
  KOYEB: 'koyeb',
  FLY: 'fly',
} as const;

/** Default preview provider */
export const DEFAULT_PREVIEW_PROVIDER = PREVIEW_PROVIDERS.KOYEB;

// **************** Create Preview App Response ****************
export interface KoyebCreateAppResponse {
  id: string;
  name: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  started_at: null;
  succeeded_at: null;
  paused_at: null;
  resumed_at: null;
  terminated_at: null;
  status: Status;
  messages: string[];
  version: string;
  domains: Domain[];
}

interface Domain {
  id: string;
  organization_id: string;
  name: string;
  created_at: string;
  updated_at: string;
  status: string;
  type: string;
  app_id: string;
  deployment_group: string;
  verified_at: null;
  intended_cname: string;
  messages: any[];
  version: string;
  cloudflare: Cloudflare;
}

// **************** Create Preview App Response ****************

// **************** Get App Details ****************
export interface KoyebAppDetails {
  app: App;
}

interface App {
  id: string;
  name: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  started_at: string;
  succeeded_at: string;
  paused_at: null;
  resumed_at: null;
  terminated_at: null;
  status: Status;
  messages: string[];
  version: string;
  domains: Domain[];
}

interface Domain {
  id: string;
  organization_id: string;
  name: string;
  created_at: string;
  updated_at: string;
  status: string;
  type: string;
  app_id: string;
  deployment_group: string;
  verified_at: null;
  intended_cname: string;
  messages: any[];
  version: string;
  cloudflare: Cloudflare;
}

type Status =
  | 'STARTING'
  | 'HEALTHY'
  | 'DEGRADED'
  | 'UNHEALTHY'
  | 'DELETING'
  | 'DELETED'
  | 'PAUSING'
  | 'PAUSED'
  | 'RESUMING';

interface Cloudflare {}
// **************** Get App Details ****************

// **************** Basic Koyeb Types ****************
export interface KoyebApp {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface KoyebService {
  id: string;
  name: string;
  app_id: string;
  status: string;
  latest_deployment_id: string;
  created_at: string;
  updated_at: string;
}

export interface KoyebDeployment {
  id: string;
  service_id: string;
  status: string;
  messages: string[];
  definition: {
    name: string;
    type: string;
    regions: string[];
    instance_types: Array<{
      type: string;
    }>;
    docker: {
      image: string;
    };
    ports: Array<{
      port: number;
      protocol: string;
    }>;
    env: Array<{
      key: string;
      value: string;
    }>;
    health_checks: Array<{
      http: {
        port: number;
        path: string;
      };
    }>;
  };
}
// **************** Basic Koyeb Types ****************

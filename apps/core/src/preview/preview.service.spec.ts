import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { PreviewService } from './preview.service';
import { KoyebPreviewManager } from './koyeb-preview-manager';
import {
  THIRTY_SEC_MS,
  FIVE_SEC_MS,
  TEST_SHORT_INTERVAL_MS,
  ONE_SEC_MS,
  TEST_INTERVAL_MS,
  TEN_SEC_MS,
  THIRTY_FIVE_SEC_MS,
  SINGLE_FILE_COUNT,
} from './constants';

// Mock the KoyebPreviewManager
jest.mock('./koyeb-preview-manager');

describe('PreviewService', () => {
  let service: PreviewService;
  let mockKoyebManager: jest.Mocked<KoyebPreviewManager>;

  beforeEach(async () => {
    // Set required environment variables
    process.env.KOYEB_API_KEY = 'test-api-key';
    process.env.DOCKER_IMAGE = 'test-docker-image';

    const module: TestingModule = await Test.createTestingModule({
      providers: [PreviewService],
    }).compile();

    service = module.get<PreviewService>(PreviewService);

    // Get the mocked KoyebPreviewManager instance
    mockKoyebManager = (service as any).koyebManager;
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.KOYEB_API_KEY;
    delete process.env.DOCKER_IMAGE;
  });

  describe('waitForAppHealthy', () => {
    it('should return immediately when app is already healthy', async () => {
      // Mock getAppDetails to return healthy status
      mockKoyebManager.getAppDetails = jest.fn().mockResolvedValue({
        app: { status: 'HEALTHY', messages: [] },
      });

      const startTime = Date.now();
      await (service as any).waitForAppHealthy(
        'test-app-id',
        FIVE_SEC_MS,
        TEST_SHORT_INTERVAL_MS,
      );
      const elapsedTime = Date.now() - startTime;

      expect(mockKoyebManager.getAppDetails).toHaveBeenCalledWith(
        'test-app-id',
      );
      expect(elapsedTime).toBeLessThan(ONE_SEC_MS); // Should complete quickly
    });

    it('should wait and retry until app becomes healthy', async () => {
      // Mock getAppDetails to return STARTING first, then HEALTHY
      mockKoyebManager.getAppDetails = jest
        .fn()
        .mockResolvedValueOnce({ app: { status: 'STARTING', messages: [] } })
        .mockResolvedValueOnce({ app: { status: 'STARTING', messages: [] } })
        .mockResolvedValueOnce({ app: { status: 'HEALTHY', messages: [] } });

      await (service as any).waitForAppHealthy(
        'test-app-id',
        FIVE_SEC_MS,
        TEST_SHORT_INTERVAL_MS,
      );

      expect(mockKoyebManager.getAppDetails).toHaveBeenCalledTimes(3);
    });

    it('should throw error when app enters terminal failure state', async () => {
      // Mock getAppDetails to return UNHEALTHY status
      mockKoyebManager.getAppDetails = jest.fn().mockResolvedValue({
        app: { status: 'UNHEALTHY', messages: ['Service failed to start'] },
      });

      await expect(
        (service as any).waitForAppHealthy(
          'test-app-id',
          FIVE_SEC_MS,
          TEST_SHORT_INTERVAL_MS,
        ),
      ).rejects.toThrow('App test-app-id entered terminal state: UNHEALTHY');
    });

    it(
      'should throw timeout error when app does not become healthy within timeout',
      async () => {
        // Mock getAppDetails to always return STARTING
        mockKoyebManager.getAppDetails = jest.fn().mockResolvedValue({
          app: { status: 'STARTING', messages: [] },
        });

        await expect(
          (service as any).waitForAppHealthy(
            'test-app-id',
            ONE_SEC_MS,
            TEST_INTERVAL_MS,
          ), // 1 second timeout, 200ms interval
        ).rejects.toThrow('App test-app-id failed to become healthy after');
      },
      TEN_SEC_MS,
    ); // Increase test timeout

    it('should handle API errors gracefully and continue retrying', async () => {
      // Mock getAppDetails to throw error first, then return healthy
      mockKoyebManager.getAppDetails = jest
        .fn()
        .mockRejectedValueOnce(new Error('API temporarily unavailable'))
        .mockResolvedValueOnce({ app: { status: 'HEALTHY', messages: [] } });

      await (service as any).waitForAppHealthy(
        'test-app-id',
        FIVE_SEC_MS,
        TEST_SHORT_INTERVAL_MS,
      );

      expect(mockKoyebManager.getAppDetails).toHaveBeenCalledTimes(2);
    });
  });

  describe('createPreview', () => {
    it(
      'should cleanup app when health check fails',
      async () => {
        const mockFiles = { 'test.js': 'console.log("test");' };

        // Mock createPreviewApp to return app details
        mockKoyebManager.createPreviewApp = jest.fn().mockResolvedValue({
          serviceId: 'test-service-id',
          appId: 'test-app-id',
          appUrl: 'https://test-app.koyeb.app',
        });

        // Mock getAppDetails to always return STARTING (will timeout)
        mockKoyebManager.getAppDetails = jest.fn().mockResolvedValue({
          app: { status: 'STARTING', messages: [] },
        });

        // Mock destroyApp
        mockKoyebManager.destroyApp = jest.fn().mockResolvedValue(undefined);

        await expect(service.createPreview(mockFiles)).rejects.toThrow(
          `App failed to become healthy within ${THIRTY_SEC_MS / 1000} seconds`,
        );

        // Verify cleanup was called
        expect(mockKoyebManager.destroyApp).toHaveBeenCalledWith('test-app-id');
      },
      THIRTY_FIVE_SEC_MS,
    ); // Increase test timeout to account for 30s health check timeout

    it('should proceed to code injection when app becomes healthy', async () => {
      const mockFiles = { 'test.js': 'console.log("test");' };

      // Mock createPreviewApp
      mockKoyebManager.createPreviewApp = jest.fn().mockResolvedValue({
        serviceId: 'test-service-id',
        appId: 'test-app-id',
        appUrl: 'https://test-app.koyeb.app',
      });

      // Mock getAppDetails to return healthy immediately
      mockKoyebManager.getAppDetails = jest.fn().mockResolvedValue({
        app: { status: 'HEALTHY', messages: [] },
      });

      // Mock injectCode
      mockKoyebManager.injectCode = jest.fn().mockResolvedValue(undefined);

      const result = await service.createPreview(mockFiles);

      expect(result).toEqual({
        serviceId: 'test-service-id',
        appId: 'test-app-id',
        previewUrl: 'https://test-app.koyeb.app',
        wsUrl: 'wss://test-app.koyeb.app/ws',
        filesCount: SINGLE_FILE_COUNT,
      });

      expect(mockKoyebManager.injectCode).toHaveBeenCalledWith(
        'https://test-app.koyeb.app',
        mockFiles,
      );
    });
  });
});

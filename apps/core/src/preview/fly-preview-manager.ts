import {
  FlyApp,
  FlyMachine,
  FlyCreateAppResponse,
  FlyCreateMachineRequest,
  FlyWaitResponse,
  FlyMachineState,
  FlyPreviewResult,
  FlyCreateAppRequest,
  FlyAppDeleteParams,
  FLY_MACHINE_PRESETS,
  FLY_SERVICE_CONFIG,
  FLY_REGIONS,
} from './fly-types';
import {
  TWO_MINUTES_MS,
  THIRTY_SEC_MS,
  HEALTH_CHECK_INTERVAL_MS,
  APP_PORT,
  INJECT_CODE_PATH,
  HEALTH_CHECK_PATH,
} from './constants';

export class FlyPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://api.machines.dev';
  private dockerImage: string;
  private orgSlug: string;

  constructor(apiKey: string, dockerImage: string, orgSlug: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
    this.orgSlug = orgSlug;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
  ): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      const responseText = await response.text();

      if (!response.ok) {
        console.error(`Fly.io API error: ${response.status} - ${responseText}`);
        throw new Error(
          `Fly.io API error: ${response.status} - ${responseText}`,
        );
      }

      try {
        return JSON.parse(responseText);
      } catch (error) {
        console.error('Failed to parse response as JSON:', responseText);
        throw new Error(
          `Failed to parse Fly.io API response: ${error.message}`,
        );
      }
    } catch (error) {
      console.error(`Request to ${endpoint} failed:`, error);
      throw error;
    }
  }

  async createApp(appName: string): Promise<FlyCreateAppResponse> {
    const payload: FlyCreateAppRequest = {
      app_name: appName,
      org_slug: this.orgSlug,
    };

    return this.makeRequest<FlyCreateAppResponse>('/v1/apps', 'POST', payload);
  }

  async createPreviewApp(): Promise<FlyPreviewResult> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const machineName = `preview-machine-${timestamp}-${randomId}`;

    // Step 1: Create App (top-level container)
    const app = await this.createApp(appName);

    // Step 2: Create Machine inside the App
    const machineConfig: FlyCreateMachineRequest = {
      name: machineName,
      config: {
        guest: FLY_MACHINE_PRESETS.SMALL,
        image: this.dockerImage,
        restart: {
          policy: 'no',
        },
        auto_destroy: true,
        services: [FLY_SERVICE_CONFIG.HTTP_SERVICE],
        env: {
          NODE_ENV: 'development',
          PORT: APP_PORT.toString(),
        },
        metadata: {
          'preview-app': 'true',
          'created-at': new Date().toISOString(),
          timeout: TWO_MINUTES_MS.toString(),
        },
      },
      region: FLY_REGIONS.SINGAPORE,
      skip_launch: false,
      skip_service_registration: false,
    };

    try {
      const machine = await this.makeRequest<FlyMachine>(
        `/v1/apps/${appName}/machines`,
        'POST',
        machineConfig,
      );

      // Step 3: Wait for the machine to be ready
      await this.waitForMachineReady(appName, machine.id);

      // Step 4: Get the App URL
      const appUrl = `https://${appName}.fly.dev`;

      // Step 5: Perform health check
      await this.performHealthCheck(appUrl);

      // Schedule auto-destruction after 2 minutes
      setTimeout(
        () => {
          this.destroyApp(appName);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || TWO_MINUTES_MS.toString()),
      );

      return {
        machineId: machine.id,
        appId: app.id,
        appUrl,
      };
    } catch (error) {
      console.error('Machine creation failed:', error);
      console.error('Machine config:', JSON.stringify(machineConfig, null, 2));

      // Clean up the app if machine creation fails
      try {
        await this.makeRequest(`/v1/apps/${appName}`, 'DELETE', {
          force: true,
        });
        console.log(`App ${appName} deleted after machine creation failure`);
      } catch (cleanupError) {
        console.error(`Failed to clean up app ${appName}:`, cleanupError);
      }

      throw error;
    }
  }

  async getMachine(appName: string, machineId: string): Promise<FlyMachine> {
    return this.makeRequest<FlyMachine>(
      `/v1/apps/${appName}/machines/${machineId}`,
    );
  }

  async waitForMachineReady(appName: string, machineId: string): Promise<void> {
    let attempts = 0;
    const maxAttempts = 24; // 4 minutes max wait (24 * 10s)

    while (attempts < maxAttempts) {
      try {
        // Wait for machine to be in 'started' state
        await this.makeRequest<FlyWaitResponse>(
          `/v1/apps/${appName}/machines/${machineId}/wait?state=started&timeout=30`,
        );

        console.log(`Machine ${machineId} is in started state`);
        return;
      } catch (error) {
        console.log(
          `Machine ${machineId} not ready yet, attempt ${attempts + 1}`,
        );
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
      }
    }

    throw new Error('Machine failed to become ready within timeout');
  }

  private async performHealthCheck(appUrl: string): Promise<void> {
    const healthUrl = `${appUrl}${HEALTH_CHECK_PATH}`;
    let attempts = 0;
    const maxAttempts = 60; // 30 seconds (60 * 500ms)

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(healthUrl, {
          method: 'GET',
          signal: AbortSignal.timeout(5000), // 5 second timeout
        });

        if (response.ok) {
          console.log('Health check passed');
          return;
        }
      } catch (error) {
        // Continue trying
      }

      await new Promise((resolve) =>
        setTimeout(resolve, HEALTH_CHECK_INTERVAL_MS),
      );
      attempts++;
    }

    throw new Error('Health check failed within timeout');
  }

  async injectCode(
    appUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    try {
      const injectionUrl = `${appUrl}${INJECT_CODE_PATH}`;

      console.log(`Attempting code injection to: ${injectionUrl}`);

      const response = await fetch(injectionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ files }),
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      if (!response.ok) {
        throw new Error(`Code injection failed: ${response.status}`);
      }

      const result = await response.json();
      console.log('Code injection successful:', result.message);
    } catch (e) {
      console.error('Code injection failed:', e);
      throw e;
    }
  }

  async destroyApp(appName: string): Promise<void> {
    try {
      const deleteParams: FlyAppDeleteParams = { force: true };

      // Delete the app - this will automatically delete all associated machines
      await this.makeRequest(`/v1/apps/${appName}`, 'DELETE', deleteParams);
      console.log(
        `App ${appName} deleted successfully (machines auto-deleted)`,
      );
    } catch (error) {
      console.error(`Failed to destroy app ${appName}:`, error);
    }
  }

  async stopMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.makeRequest(
        `/v1/apps/${appName}/machines/${machineId}/stop`,
        'POST',
        { timeout: '30s' },
      );
      console.log(`Machine ${machineId} stopped successfully`);
    } catch (error) {
      console.error(`Failed to stop machine ${machineId}:`, error);
    }
  }

  async deleteMachine(appName: string, machineId: string): Promise<void> {
    try {
      await this.makeRequest(
        `/v1/apps/${appName}/machines/${machineId}`,
        'DELETE',
        { force: true },
      );
      console.log(`Machine ${machineId} deleted successfully`);
    } catch (error) {
      console.error(`Failed to delete machine ${machineId}:`, error);
    }
  }
}

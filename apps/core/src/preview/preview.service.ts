import { Injectable, Logger } from '@nestjs/common';
import { KoyebPreviewManager } from './koyeb-preview-manager';
import {
  THIRTY_SEC_MS,
  HEALTH_CHECK_INTERVAL_MS,
  HEALTHY_STATUS,
  UNHEALTHY_STATUS,
  DELETED_STATUS,
  MIN_MESSAGES_LENGTH,
} from './constants';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private koyebManager: KoyebPreviewManager;

  constructor() {
    const apiKey = process.env.KOYEB_API_KEY;
    const dockerImage = process.env.DOCKER_IMAGE;

    if (!apiKey) {
      throw new Error('KOYEB_API_KEY environment variable is required');
    }

    if (!dockerImage) {
      throw new Error('DOCKER_IMAGE environment variable is required');
    }

    this.koyebManager = new KoyebPreviewManager(apiKey, dockerImage);
    this.logger.log('PreviewService initialized with Koyeb integration');
  }

  async createPreview(files: Record<string, string>) {
    this.logger.log('Starting preview creation process');

    try {
      // Step 1: Create Koyeb app with web service (2-3 seconds)
      this.logger.log('Creating Koyeb app with web service...');

      const createResult = await this.koyebManager.createPreviewApp();

      const { serviceId, appId, appUrl } = createResult;

      // Step 2: Files are already in the correct format
      this.logger.log(
        `Processing ${Object.keys(files).length} files for injection`,
      );

      // Step 2.5: Wait for app to become healthy with proper timeout and cleanup
      this.logger.log('Waiting for app to become healthy...');
      try {
        await this.waitForAppHealthy(appId, THIRTY_SEC_MS); // 30 seconds timeout
        this.logger.log('App is now healthy and ready for code injection');
      } catch (error) {
        this.logger.error(
          'App failed to become healthy within timeout:',
          error,
        );

        // Clean up the app since it failed to start properly
        try {
          await this.koyebManager.destroyApp(appId);
          this.logger.log(`App ${appId} destroyed due to health check failure`);
        } catch (cleanupError) {
          this.logger.error(`Failed to cleanup app ${appId}:`, cleanupError);
        }

        throw new Error(
          `App failed to become healthy within ${THIRTY_SEC_MS / 1000} seconds: ${error.message}`,
        );
      }

      // Step 3: Inject code into running container (1 second)
      this.logger.log('Injecting code into container...');
      await this.koyebManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      const result = {
        serviceId,
        appId,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
      };

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  /**
   * Wait for app to become healthy with configurable timeout and polling interval
   * @param appId - The Koyeb app ID to monitor
   * @param timeoutMs - Maximum time to wait in milliseconds (default: 30000)
   * @param intervalMs - Polling interval in milliseconds (default: 500)
   */
  private async waitForAppHealthy(
    appId: string,
    timeoutMs: number = THIRTY_SEC_MS,
    intervalMs: number = HEALTH_CHECK_INTERVAL_MS,
  ): Promise<void> {
    const startTime = Date.now();
    const maxAttempts = Math.ceil(timeoutMs / intervalMs);
    let attempts = 0;

    this.logger.log(
      `Starting health check for app ${appId} (timeout: ${timeoutMs}ms, interval: ${intervalMs}ms)`,
    );

    while (attempts < maxAttempts) {
      const elapsedTime = Date.now() - startTime;

      // Check if we've exceeded the timeout
      if (elapsedTime >= timeoutMs) {
        throw new Error(
          `Health check timeout exceeded (${timeoutMs}ms). App ${appId} did not become healthy.`,
        );
      }

      try {
        this.logger.log(
          `Health check attempt ${attempts + 1}/${maxAttempts} for app ${appId} (elapsed: ${elapsedTime}ms)`,
        );

        const appDetails = await this.koyebManager.getAppDetails(appId);
        const appStatus = appDetails.app.status;

        this.logger.log(`App ${appId} current status: ${appStatus}`);

        // Check if app is healthy
        if (appStatus === HEALTHY_STATUS) {
          this.logger.log(
            `App ${appId} is healthy after ${elapsedTime}ms (${attempts + 1} attempts)`,
          );
          return;
        }

        // Check for terminal failure states
        if (appStatus === UNHEALTHY_STATUS || appStatus === DELETED_STATUS) {
          throw new Error(
            `App ${appId} entered terminal state: ${appStatus}. Cannot proceed.`,
          );
        }

        // Log current status for debugging
        if (
          appDetails.app.messages &&
          appDetails.app.messages.length > MIN_MESSAGES_LENGTH
        ) {
          this.logger.log(
            `App ${appId} messages: ${appDetails.app.messages.join(', ')}`,
          );
        }
      } catch (error) {
        // If it's our custom error (terminal state), re-throw it
        if (
          error.message.includes('terminal state') ||
          error.message.includes('timeout exceeded')
        ) {
          throw error;
        }

        // For API errors, log and continue trying
        this.logger.warn(
          `Health check attempt ${attempts + 1} failed for app ${appId}: ${error.message}`,
        );
      }

      attempts++;

      // Wait before next attempt (unless this was the last attempt)
      if (attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, intervalMs));
      }
    }

    // If we exit the loop without returning, we've exceeded max attempts
    throw new Error(
      `App ${appId} failed to become healthy after ${maxAttempts} attempts over ${timeoutMs}ms`,
    );
  }

  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  }
}

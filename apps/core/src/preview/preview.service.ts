import { Injectable, Logger } from '@nestjs/common';
import { FlyPreviewManager } from './fly-preview-manager';

@Injectable()
export class PreviewService {
  private readonly logger = new Logger(PreviewService.name);
  private flyManager: FlyPreviewManager;

  constructor() {
    const apiKey = process.env.FLY_API_TOKEN;
    const dockerImage = process.env.DOCKER_IMAGE;
    const orgSlug = process.env.FLY_ORG_SLUG;

    if (!apiKey) {
      throw new Error('FLY_API_TOKEN environment variable is required');
    }

    if (!dockerImage) {
      throw new Error('DOCKER_IMAGE environment variable is required');
    }

    if (!orgSlug) {
      throw new Error('FLY_ORG_SLUG environment variable is required');
    }

    this.flyManager = new FlyPreviewManager(apiKey, dockerImage, orgSlug);
    this.logger.log('PreviewService initialized with Fly.io integration');
  }

  async createPreview(files: Record<string, string>) {
    this.logger.log('Starting preview creation process with Fly.io');

    try {
      // Step 1: Create Fly.io app with machine (2-3 seconds)
      this.logger.log('Creating Fly.io app with machine...');

      const createResult = await this.flyManager.createPreviewApp();

      const { machineId, appId, appUrl } = createResult;

      // Step 2: Files are already in the correct format
      this.logger.log(
        `Processing ${Object.keys(files).length} files for injection`,
      );

      // Validate files
      if (!files || Object.keys(files).length === 0) {
        throw new Error('No files provided for preview');
      }

      // Step 3: Inject code into running machine (1 second)
      this.logger.log('Injecting code into machine...');
      await this.flyManager.injectCode(appUrl, files);
      this.logger.log('Code injection completed successfully');

      const result = {
        serviceId: machineId, // For compatibility with existing frontend
        appId,
        previewUrl: appUrl,
        wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
        filesCount: Object.keys(files).length,
        provider: 'fly',
      };

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to create preview:', error);
      throw new Error(`Preview creation failed: ${error.message}`);
    }
  }

  async healthCheck() {
    return {
      status: 'ok',
      service: 'fly-preview',
      timestamp: new Date().toISOString(),
    };
  }
}

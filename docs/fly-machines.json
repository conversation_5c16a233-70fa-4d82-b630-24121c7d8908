[{"documentation": [{"title": "Machines", "endpoints": [{"url": "/v1/apps/{app_name}/machines", "method": "GET", "response": {"200": {"id": "string", "name": "string", "state": "string", "config": "object", "events": [{"type": "string", "source": "string", "status": "string", "timestamp": "integer"}], "region": "string", "image_ref": {"tag": "string", "digest": "string", "registry": "string", "repository": "string"}, "created_at": "string", "private_ip": "string", "updated_at": "string", "instance_id": "string"}}, "parameters": {"region": "string", "app_name": "string", "metadata.{key}": "string", "include_deleted": "boolean"}}, {"url": "/v1/apps/{app_name}/machines", "method": "POST", "response": {"200": {"id": "string", "name": "string", "state": "string", "config": "object", "events": [{"type": "string", "source": "string", "status": "string", "timestamp": "integer"}], "region": "string", "image_ref": {"tag": "string", "digest": "string", "registry": "string", "repository": "string"}, "created_at": "string", "private_ip": "string", "updated_at": "string", "instance_id": "string"}}, "parameters": {"name": "string", "config": {"init": {"exec": ["string", "string"]}, "guest": {"cpus": "integer", "cpu_kind": "string", "memory_mb": "integer"}, "image": "string", "restart": {"policy": "string"}, "auto_destroy": "boolean"}, "region": "string", "app_name": "string", "lease_ttl": "integer", "skip_launch": "boolean", "skip_service_registration": "boolean"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/wait", "method": "GET", "response": {"200": {"ok": "boolean"}}, "parameters": {"state": "string", "timeout": "integer", "app_name": "string", "machine_id": "string", "instance_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}", "method": "GET", "response": {"200": {"id": "string", "name": "string", "state": "string", "config": "object", "events": [{"type": "string", "source": "string", "status": "string", "timestamp": "integer"}], "region": "string", "image_ref": {"tag": "string", "digest": "string", "registry": "string", "repository": "string"}, "created_at": "string", "private_ip": "string", "updated_at": "string", "instance_id": "string"}}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}", "method": "POST", "response": {"200": {"id": "string", "name": "string", "state": "string", "config": "object", "region": "string", "image_ref": "object", "created_at": "string", "private_ip": "string", "updated_at": "string", "instance_id": "string"}}, "parameters": {"name": "string", "config": "object", "region": "string", "app_name": "string", "lease_ttl": "integer", "machine_id": "string", "skip_launch": "boolean", "current_version": "string", "skip_service_registration": "boolean"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/stop", "method": "POST", "response": {"200": {"ok": "boolean"}}, "parameters": {"signal": "string", "timeout": "string", "app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/suspend", "method": "POST", "response": {"200": {"ok": "boolean"}}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/start", "method": "POST", "response": {"200": {"migrated": "boolean", "new_host": "string", "previous_state": "string"}}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}", "method": "DELETE", "response": {"200": {"ok": "boolean"}}, "parameters": {"force": "boolean", "app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/lease", "method": "POST", "response": {"201": {"data": {"nonce": "string", "owner": "string", "version": "string", "expires_at": "integer", "description": "string"}, "status": "string"}}, "parameters": {"ttl": "integer", "app_name": "string", "machine_id": "string", "description": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/lease", "method": "GET", "response": {"200": {"data": {"nonce": "string", "owner": "string", "version": "string", "expires_at": "integer", "description": "string"}, "status": "string"}}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/lease", "method": "DELETE", "response": {"200": {"data": {"ok": "boolean"}, "status": "string"}}, "parameters": {"app_name": "string", "machine_id": "string", "fly-machine-lease-nonce": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/cordon", "method": "POST", "response": {"200": {"ok": "boolean"}}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/uncordon", "method": "POST", "response": {"200": {"ok": "boolean"}}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/metadata", "method": "GET", "response": {"200": "object"}, "parameters": {"app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/metadata/{key}", "method": "POST", "response": {"204": {}}, "parameters": {"key": "string", "value": "string", "app_name": "string", "machine_id": "string"}}, {"url": "/v1/apps/{app_name}/machines/{machine_id}/metadata/{key}", "method": "DELETE", "response": {"204": {}}, "parameters": {"key": "string", "app_name": "string", "machine_id": "string"}}], "description": "You can use the Machines resource to create, stop, start, update, and delete Fly Machines. Fly Machines are fast-launching VMs on Fly.io. The Machine resource is the configuration and state for a Machine."}]}]